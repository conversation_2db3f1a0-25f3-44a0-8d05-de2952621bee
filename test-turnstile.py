import requests
from bs4 import BeautifulSoup
import urllib3
import ssl
import socket
from requests.adapters import HTTPAdapter
from urllib3.poolmanager import PoolManager
from urllib3.util.ssl_ import create_urllib3_context
import time
import os
import concurrent.futures
import threading
from datetime import datetime
import sys

# 禁用 SSL 警告（仅在测试环境使用）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 自定义SSL适配器
class SSLAdapter(HTTPAdapter):
    def init_poolmanager(self, *args, **kwargs):
        ctx = create_urllib3_context()
        ctx.check_hostname = False
        ctx.verify_mode = ssl.CERT_NONE
        # 设置最低TLS版本为TLSv1.2
        ctx.minimum_version = ssl.TLSVersion.TLSv1_2
        kwargs['ssl_context'] = ctx
        return super(SSLAdapter, self).init_poolmanager(*args, **kwargs)

def check_turnstile(domain, verbose=False):
    """
    检查域名是否启用了 Cloudflare Turnstile
    verbose: 是否显示详细输出
    """
    url = f"https://{domain}"
    try:
        # 配置请求选项，模拟真实浏览器
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        # 创建会话
        session = requests.Session()
        
        # 首先尝试正常的 SSL 验证
        try:
            response = session.get(url, headers=headers, timeout=10)
        except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as e:
            if verbose:
                print(f"SSL 验证失败，尝试使用自定义SSL配置...")
            
            # 使用自定义SSL适配器
            session.mount('https://', SSLAdapter())
            
            try:
                # 再次尝试，不验证证书
                response = session.get(url, headers=headers, verify=False, timeout=10)
            except Exception as e2:
                if verbose:
                    print(f"使用自定义SSL配置仍然失败")
                
                # 尝试使用HTTP而不是HTTPS
                try:
                    http_url = f"http://{domain}"
                    if verbose:
                        print(f"尝试使用HTTP: {http_url}")
                    response = session.get(http_url, headers=headers, timeout=10)
                except Exception as e3:
                    if verbose:
                        print(f"HTTP请求也失败: {e3}")
                    return False

        # 打印响应状态码和头信息
        if verbose:
            print(f"响应状态码: {response.status_code}")
            print(f"Server: {response.headers.get('Server', 'N/A')}")
        
        # 检查是否是Cloudflare挑战页面
        if response.status_code == 403 and 'cloudflare' in response.headers.get('Server', '').lower():
            if verbose:
                print(f"\n检测到Cloudflare挑战页面 (403)")
                print(f"Cloudflare Ray ID: {response.headers.get('CF-RAY', 'N/A')}")
            return True
        elif response.status_code != 200:
            if verbose:
                print(f"无法访问 {domain}，状态码：{response.status_code}")
            return False

        # 使用 BeautifulSoup 解析 HTML 内容
        soup = BeautifulSoup(response.text, 'html.parser')

        # 搜索 Turnstile 的相关标记，主要通过查找 'turnstile' 相关的元素
        if soup.find('script', {'src': lambda x: x and 'turnstile' in x}):
            if verbose:
                print(f"网站 {domain} 启用了 Cloudflare Turnstile.")
            return True

        # 如果页面中没有相关的 script 标签，返回未启用的信息
        if verbose:
            print(f"网站 {domain} 没有启用 Cloudflare Turnstile.")
        return False
    except requests.exceptions.RequestException as e:
        if verbose:
            print(f"请求失败: {e}")
        return False

def parse_global_conf(url='https://raw.githubusercontent.com/Repcz/Tool/refs/heads/X/Surge/Rules/ProxyGFW.list'):
    """从指定的URL中解析域名"""
    domains_to_test = []
    lines_with_domains = {}  # 保存域名和对应的原始行
    
    try:
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        lines = response.text.split('\n')
        for line in lines:
            line = line.strip()
            # 跳过空行和注释
            if not line or line.startswith('#'):
                continue
            
            # 检查是否以 DOMAIN 或 DOMAIN-SUFFIX 开头
            if line.startswith('DOMAIN,') or line.startswith('DOMAIN-SUFFIX,'):
                parts = line.split(',', 1)
                if len(parts) >= 2:
                    domain = parts[1].strip()
                    # 检查域名是否包含 "."
                    if '.' in domain:
                        domains_to_test.append(domain)
                        lines_with_domains[domain] = line
                        # else:
                        #     print(f"跳过不包含'.' 的域名: {domain}")
    
    except requests.RequestException as e:
        print(f"错误: 无法从 {url} 获取数据 - {e}")
        return [], {}
    
    return domains_to_test, lines_with_domains

def load_predefined_domains(predefined_file='predefined_domains.txt'):
    """从文件中加载预定义的域名"""
    predefined_domains = []
    try:
        if os.path.exists(predefined_file):
            with open(predefined_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        predefined_domains.append(line)
            print(f"从 {predefined_file} 加载了 {len(predefined_domains)} 个预定义域名")
        else:
            print(f"警告: 预定义域名文件 {predefined_file} 不存在")
    except Exception as e:
        print(f"读取预定义域名文件时出错: {e}")

    return predefined_domains

def save_turnstile_domains(turnstile_domains, lines_with_domains, output_file='turnstile-rules/turnstile.txt', predefined_file='predefined_domains.txt'):
    """保存启用了 Turnstile 的域名到文件"""
    # 确保目录存在
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    # 加载预定义域名
    predefined_domains = load_predefined_domains(predefined_file)

    with open(output_file, 'w', encoding='utf-8') as f:
        # 首先写入预定义的域名
        for domain_line in predefined_domains:
            f.write(domain_line + "\n")

        # 写入启用了 Turnstile 的域名对应的原始行
        for domain in sorted(turnstile_domains):
            if domain in lines_with_domains:
                f.write(lines_with_domains[domain] + "\n")

    print(f"\n结果已保存到: {output_file}")
    if predefined_domains:
        print(f"包含 {len(predefined_domains)} 个预定义域名和 {len(turnstile_domains)} 个检测到的 Turnstile 域名")

def test_domain_with_progress(domain, progress_info):
    """测试单个域名并更新进度"""
    index, total, lock, results = progress_info
    try:
        result = check_turnstile(domain)
        with lock:
            results['tested_count'] += 1
            if result:
                results['turnstile_domains'].append(domain)
                print(f"[{index}/{total}] ✓ {domain:<40} [启用 Turnstile]")
            else:
                # 只在详细模式下显示未启用的域名
                if results.get('verbose', False):
                    print(f"[{index}/{total}] ✗ {domain:<40} [未启用]")
        return domain, result
    except Exception as e:
        with lock:
            print(f"[{index}/{total}] ❌ {domain:<40} [错误: {str(e)}]")
        return domain, None

def get_optimal_workers():
    """根据环境确定最佳工作线程数"""
    # 检测是否在 GitHub Actions 环境中运行
    if os.environ.get('GITHUB_ACTIONS') == 'true':
        # GitHub Actions 提供的硬件配置
        # - Linux: 2-core CPU, 7 GB RAM
        # - Windows: 2-core CPU, 7 GB RAM  
        # - macOS: 3-core CPU, 14 GB RAM
        runner_os = os.environ.get('RUNNER_OS', '').lower()
        if runner_os == 'macos':
            return 20  # macOS runners 有更多资源
        else:
            return 15  # Linux/Windows runners
    else:
        # 本地环境，使用保守的值
        cpu_count = os.cpu_count() or 4
        return min(cpu_count * 2, 10)

def main():
    """主函数"""
    start_time = datetime.now()
    
    print("Cloudflare Turnstile 批量检测工具")
    print("=" * 50)
    
    # 显示运行环境信息
    if os.environ.get('GITHUB_ACTIONS') == 'true':
        print(f"运行环境: GitHub Actions ({os.environ.get('RUNNER_OS', 'Unknown')} Runner)")
    else:
        print(f"运行环境: 本地 ({sys.platform})")
    
    # 确定工作线程数
    max_workers = get_optimal_workers()
    print(f"并行工作线程数: {max_workers}")
    
    # 从 URL 获取并解析 global.conf
    print("\n正在从 https://raw.githubusercontent.com/Repcz/Tool/refs/heads/X/Surge/Rules/ProxyGFW.list 获取数据...")
    domains_to_test, lines_with_domains = parse_global_conf()
    
    if not domains_to_test:
        print("没有找到需要测试的域名")
        return
    
    print(f"\n总共找到 {len(domains_to_test)} 个域名需要测试")
    print("\n开始并行测试...")
    print("-" * 50)
    
    # 共享的结果存储
    lock = threading.Lock()
    results = {
        'turnstile_domains': [],
        'tested_count': 0,
        'verbose': False  # 设置为 True 以显示所有测试结果
    }
    
    # 使用线程池并行执行测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_domain = {}
        for i, domain in enumerate(domains_to_test, 1):
            progress_info = (i, len(domains_to_test), lock, results)
            future = executor.submit(test_domain_with_progress, domain, progress_info)
            future_to_domain[future] = domain
        
        # 等待所有任务完成
        concurrent.futures.wait(future_to_domain)
    
    # 计算执行时间
    end_time = datetime.now()
    execution_time = (end_time - start_time).total_seconds()
    
    # 显示统计信息
    print("\n" + "=" * 50)
    print("测试完成！")
    print(f"\n统计:")
    print(f"- 总计测试: {results['tested_count']} 个域名")
    print(f"- 启用 Turnstile: {len(results['turnstile_domains'])} 个")
    print(f"- 未启用 Turnstile: {results['tested_count'] - len(results['turnstile_domains'])} 个")
    print(f"- 执行时间: {execution_time:.2f} 秒")
    print(f"- 平均速度: {len(domains_to_test) / execution_time:.2f} 个域名/秒")
    
    # 保存结果
    if results['turnstile_domains']:
        save_turnstile_domains(results['turnstile_domains'], lines_with_domains, predefined_file='predefined_domains.txt')
        print(f"\n启用 Turnstile 的域名:")
        for domain in sorted(results['turnstile_domains']):
            print(f"  - {domain}")
    else:
        print("\n没有发现启用 Turnstile 的域名")

if __name__ == "__main__":
    main()
