import requests
import dns.resolver
import socket
from ipwhois import IPWhois
from typing import List, Dict, Optional

# Cloudflare 官方发布的 IP 地址范围
# 来源: https://www.cloudflare.com/ips/
CLOUDFLARE_IPV4_RANGES = [
    '************/20', '************/22', '************/22',
    '**********/22', '************/18', '*************/18',
    '************/20', '************/20', '*************/22',
    '************/17', '***********/15', '**********/13',
    '**********/12', '**********/13', '**********/22'
]

# 简化版的 IPv6 范围，实际范围更广
CLOUDFLARE_IPV6_RANGES = [
    '2400:cb00::/32', '2606:4700::/32', '2803:f800::/32',
    '2405:b500::/32', '2405:8100::/32', '2a06:98c0::/29',
    '2c0f:f248::/32'
]


def check_headers(domain: str) -> (bool, str):
    """通过检查HTTP响应头来判断是否使用Cloudflare"""
    try:
        # 使用 allow_redirects=True 并设置超时
        response = requests.get(f"https://{domain}", timeout=10, allow_redirects=True)
        headers = response.headers

        # 最强的指纹
        if 'CF-RAY' in headers:
            return True, f"发现 'CF-RAY' 响应头: {headers['CF-RAY']}"

        # 较强的指纹
        if 'Server' in headers and 'cloudflare' in headers['Server'].lower():
            return True, f"发现 'Server' 响应头为: {headers['Server']}"

        # 其他相关指纹
        if 'CF-Cache-Status' in headers:
            return True, f"发现 'CF-Cache-Status' 响应头: {headers['CF-Cache-Status']}"

        cookies = response.cookies.get_dict()
        if '__cf_bm' in cookies or '__cflb' in cookies:
            return True, f"在Cookie中发现Cloudflare相关项: {list(cookies.keys())}"

    except requests.RequestException as e:
        return False, f"HTTP请求失败: {e}"

    return False, "在HTTP响应头中未发现明确的Cloudflare指纹。"


def check_dns(domain: str) -> (bool, str):
    """通过检查DNS记录来判断"""
    try:
        # 提取根域名以查询NS记录
        parts = domain.split('.')
        root_domain = '.'.join(parts[-2:]) if len(parts) > 1 else domain

        ns_records = dns.resolver.resolve(root_domain, 'NS')
        for record in ns_records:
            if 'ns.cloudflare.com' in str(record).lower():
                return True, f"发现NS记录指向Cloudflare: {str(record)}"
    except (dns.resolver.NoAnswer, dns.resolver.NXDOMAIN, dns.resolver.Timeout):
        # 没有NS记录或域名不存在，继续检查其他
        pass
    except Exception as e:
        return False, f"DNS查询NS记录时出错: {e}"

    return False, "NS记录未指向Cloudflare。"


def check_ip_address(domain: str) -> (bool, str):
    """通过IP地址归属来判断"""
    try:
        ip_address = socket.gethostbyname(domain)

        # 使用 ipwhois 获取 ASN 信息
        obj = IPWhois(ip_address)
        results = obj.lookup_rdap()
        asn_description = results.get('asn_description', '').lower()

        if 'cloudflare' in asn_description:
            return True, f"IP {ip_address} 的ASN信息为 '{results.get('asn_description')}'，属于Cloudflare。"

    except socket.gaierror:
        return False, "无法解析域名到IP地址。"
    except Exception as e:
        return False, f"IP地址查询时出错: {e}"

    return False, "IP地址不属于已知的Cloudflare ASN。"


def is_using_cloudflare(domain: str):
    """
    主函数，综合多种方法判断一个网站是否使用Cloudflare。
    """
    print(f"正在分析域名: {domain}\n" + "=" * 30)

    # 1. 检查HTTP头 (最快最直接)
    is_cf, reason = check_headers(domain)
    print(f"[1] HTTP响应头检查:\n    -> 结果: {'是' if is_cf else '否'}\n    -> 原因: {reason}\n")
    if is_cf:
        print("结论: 网站使用了Cloudflare。")
        return

    # 2. 检查DNS NS记录
    is_cf, reason = check_dns(domain)
    print(f"[2] DNS NS记录检查:\n    -> 结果: {'是' if is_cf else '否'}\n    -> 原因: {reason}\n")
    if is_cf:
        print("结论: 网站使用了Cloudflare。")
        return

    # 3. 检查IP地址归属
    is_cf, reason = check_ip_address(domain)
    print(f"[3] IP地址归属检查 (ASN):\n    -> 结果: {'是' if is_cf else '否'}\n    -> 原因: {reason}\n")
    if is_cf:
        print("结论: 网站使用了Cloudflare。")
        return

    print("=" * 30)
    print("结论: 根据现有检查，未能明确判断该网站使用Cloudflare，可能未使用或使用了隐藏性更好的企业级方案。")


if __name__ == '__main__':
    # --- 使用示例 ---
    # 一个明确使用Cloudflare的网站
    is_using_cloudflare('discord.com')

    print("\n" + "#" * 40 + "\n")

    # 一个未使用Cloudflare的网站
    is_using_cloudflare('baidu.com')