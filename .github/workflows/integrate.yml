name: Integrate Surge Modules
on:
  workflow_dispatch:
  schedule:
    - cron:  '30 */12 * * *'

jobs:
  run-python-script:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.8'

    - name: Install dependencies
      run: |
        pip install requests GitPython beautifulsoup4 urllib3

    - name: Run Python scripts with multi-processing
      run: |
        # Execute scripts leveraging multiple processes or tabs
        echo 'Running scripts with optimization for multiple processes...'
        python integrate.py &
        python convert.py &
        python convert-sni.py &
        python extract-hosts.py &
        python convert-game.py &
        python convert-adguard.py &
        python location-detected.py &
        python test-turnstile.py &
        wait
        echo 'Scripts execution completed.'

    - name: Commit and push changes
      uses: EndBug/add-and-commit@v9
      with:
        author_name: 'GitHub Actions'
        author_email: '<EMAIL>'
        message: 'Update'
        push: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Push to target repository
      run: |
        # 创建临时目录
        mkdir -p temp_dir
          
        # 复制需要推送的文件到临时目录
        cp -r surge-rules temp_dir/ || echo "surge-rules not found"
        cp -r game-ip-rules temp_dir/ || echo "game-ip-rules not found"
        cp -r adguard-rules temp_dir/ || echo "adguard-rules not found"
        cp -r location-detected-rules temp_dir/ || echo "location-detected-rules not found"
        cp -r turnstile-rules temp_dir/ || echo "turnstile-rules not found"
        cp config.json temp_dir/ || echo "config.json not found"
        cp reject-sni.list temp_dir/ || echo "reject-sni.list not found"
        cp modules.sgmodule temp_dir/ || echo "modules.sgmodule not found"
        
        # 初始化 git 并推送到目标仓库
        cd temp_dir
        git init
        git config user.name "GitHub Actions"
        git config user.email "<EMAIL>"
        git add .
        
        # 确保有文件被添加
        if [ -n "$(git status --porcelain)" ]; then
          # 明确创建 main 分支
          git commit -m "Update"
          git branch -M main
          
          # 推送到目标仓库
          git remote add origin https://${{ secrets.DEPLOY_TOKEN }}@github.com/fantasyxby/gadgets.git
          git push -f origin main
        else
          echo "没有找到任何文件可提交，跳过推送步骤"
        fi