#!/usr/bin/env python3
"""
简单测试脚本，验证 test-cloudflare.py 的基本功能
"""

import sys
import os

# 添加当前目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入修改后的模块
    import importlib.util
    spec = importlib.util.spec_from_file_location("test_cloudflare", "test-cloudflare.py")
    test_cloudflare = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(test_cloudflare)

    check_cloudflare = test_cloudflare.check_cloudflare
    parse_global_conf = test_cloudflare.parse_global_conf
    
    print("测试 1: 检查单个域名")
    print("-" * 30)
    
    # 测试一个已知使用 Cloudflare 的域名
    test_domain = "discord.com"
    result = check_cloudflare(test_domain, verbose=True)
    print(f"域名 {test_domain} 使用 Cloudflare: {result}")
    
    print("\n测试 2: 解析域名列表")
    print("-" * 30)
    
    # 测试解析功能（只获取前5个域名进行测试）
    domains, lines = parse_global_conf()
    if domains:
        print(f"成功解析到 {len(domains)} 个域名")
        print("前5个域名:")
        for i, domain in enumerate(domains[:5], 1):
            print(f"  {i}. {domain}")
    else:
        print("未能解析到域名")
    
    print("\n基本功能测试完成！")
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保 test-cloudflare.py 文件存在且语法正确")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
